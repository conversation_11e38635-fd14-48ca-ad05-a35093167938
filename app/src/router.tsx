import { createBrowserRouter, Navigate } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { AuthGuard, PublicRoute } from './lib/auth-guard'
import { LoadingSpinner } from './components/ui/loading-spinner'

// Lazy load page components for code splitting
const LoginPage = lazy(() => import('./pages/auth/LoginPage').then(m => ({ default: m.LoginPage })))
const SignupPage = lazy(() => import('./pages/auth/SignupPage').then(m => ({ default: m.SignupPage })))

// Admin pages
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard').then(m => ({ default: m.AdminDashboard })))
const UserManagementPage = lazy(() => import('./pages/admin/UserManagementPage').then(m => ({ default: m.UserManagementPage })))
const ApiUsageDashboard = lazy(() => import('./pages/admin/ApiUsageDashboard').then(m => ({ default: m.ApiUsageDashboard })))
const AdminSettings = lazy(() => import('./pages/admin/AdminSettings').then(m => ({ default: m.AdminSettings })))

// User pages
const UserDashboard = lazy(() => import('./pages/user/UserDashboard'))
const ApiKeysPage = lazy(() => import('./pages/user/ApiKeysPage'))
const UsageAnalyticsPage = lazy(() => import('./pages/user/UsageAnalyticsPage'))
const ProfilePage = lazy(() => import('./pages/user/ProfilePage'))

// Wrapper component for lazy loading with suspense
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
)

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/login" replace />,
  },
  {
    path: '/login',
    element: (
      <PublicRoute>
        <LazyWrapper>
          <LoginPage />
        </LazyWrapper>
      </PublicRoute>
    ),
  },
  {
    path: '/signup',
    element: (
      <PublicRoute>
        <LazyWrapper>
          <SignupPage />
        </LazyWrapper>
      </PublicRoute>
    ),
  },
  {
    path: '/dashboard',
    element: (
      <AuthGuard requiredRole="user">
        <LazyWrapper>
          <UserDashboard />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/dashboard/api-keys',
    element: (
      <AuthGuard requiredRole="user">
        <LazyWrapper>
          <ApiKeysPage />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/dashboard/analytics',
    element: (
      <AuthGuard requiredRole="user">
        <LazyWrapper>
          <UsageAnalyticsPage />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/dashboard/profile',
    element: (
      <AuthGuard requiredRole="user">
        <LazyWrapper>
          <ProfilePage />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/admin/dashboard',
    element: (
      <AuthGuard requiredRole="admin">
        <LazyWrapper>
          <AdminDashboard />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/admin/users',
    element: (
      <AuthGuard requiredRole="admin">
        <LazyWrapper>
          <UserManagementPage />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/admin/api-usage',
    element: (
      <AuthGuard requiredRole="admin">
        <LazyWrapper>
          <ApiUsageDashboard />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '/admin/settings',
    element: (
      <AuthGuard requiredRole="admin">
        <LazyWrapper>
          <AdminSettings />
        </LazyWrapper>
      </AuthGuard>
    ),
  },
  {
    path: '*',
    element: <Navigate to="/login" replace />,
  },
])