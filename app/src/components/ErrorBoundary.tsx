import React, { Component } from 'react'
import type { ErrorInfo, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId?: string
}

interface ErrorReport {
  errorId: string
  message: string
  stack?: string
  componentStack?: string
  timestamp: number
  url: string
  userAgent: string
  userId?: string
  buildVersion?: string
}

class ErrorBoundary extends Component<Props, State> {
  private errorReports: ErrorReport[] = []

  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    // Update state with error info
    this.setState({
      error,
      errorInfo
    })

    // Create error report
    const errorReport: ErrorReport = {
      errorId: this.state.errorId || `error_${Date.now()}`,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      buildVersion: process.env['VITE_BUILD_VERSION'] || 'unknown'
    }

    // Store error report
    this.errorReports.push(errorReport)
    
    // Keep only last 10 error reports
    if (this.errorReports.length > 10) {
      this.errorReports = this.errorReports.slice(-10)
    }

    // Send to error reporting service
    this.reportError(errorReport)

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  private reportError(errorReport: ErrorReport) {
    // In production, send to error reporting service
    if (process.env['NODE_ENV'] === 'production') {
      // This would typically send to services like Sentry, LogRocket, etc.
      // For now, we'll store in localStorage for debugging
      try {
        const existingReports = JSON.parse(localStorage.getItem('error_reports') || '[]')
        existingReports.push(errorReport)
        
        // Keep only last 50 reports
        const recentReports = existingReports.slice(-50)
        localStorage.setItem('error_reports', JSON.stringify(recentReports))
      } catch (e) {
        console.error('Failed to store error report:', e)
      }
    }

    // Log to console in development
    if (process.env['NODE_ENV'] === 'development') {
      console.group('🚨 Error Report')
      console.error('Error ID:', errorReport.errorId)
      console.error('Message:', errorReport.message)
      console.error('Stack:', errorReport.stack)
      console.error('Component Stack:', errorReport.componentStack)
      console.error('URL:', errorReport.url)
      console.error('Timestamp:', new Date(errorReport.timestamp).toISOString())
      console.groupEnd()
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: undefined
    })
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private handleReportBug = () => {
    const errorReport = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      url: window.location.href,
      timestamp: Date.now()
    }

    // Copy error report to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {
      alert('Error report copied to clipboard. Please paste it when reporting the bug.')
    }).catch(() => {
      // Fallback: show error report in alert
      alert(`Error Report:\n${JSON.stringify(errorReport, null, 2)}`)
    })
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900">
                Something went wrong
              </CardTitle>
              <CardDescription className="text-gray-600">
                We encountered an unexpected error. This has been reported to our team.
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {process.env['NODE_ENV'] === 'development' && this.state.error && (
                <div className="rounded-md bg-red-50 p-3">
                  <div className="text-sm text-red-800">
                    <strong>Error:</strong> {this.state.error.message}
                  </div>
                  {this.state.errorId && (
                    <div className="text-xs text-red-600 mt-1">
                      ID: {this.state.errorId}
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-col space-y-2">
                <Button onClick={this.handleRetry} className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                
                <Button variant="outline" onClick={this.handleGoHome} className="w-full">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Button>
                
                <Button variant="ghost" onClick={this.handleReportBug} className="w-full">
                  <Bug className="mr-2 h-4 w-4" />
                  Report Bug
                </Button>
              </div>

              {process.env['NODE_ENV'] === 'development' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                    Technical Details
                  </summary>
                  <div className="mt-2 text-xs text-gray-500 font-mono bg-gray-100 p-2 rounded overflow-auto max-h-40">
                    {this.state.error?.stack}
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }

  // Static method to get error reports
  static getErrorReports(): ErrorReport[] {
    try {
      return JSON.parse(localStorage.getItem('error_reports') || '[]')
    } catch {
      return []
    }
  }

  // Static method to clear error reports
  static clearErrorReports(): void {
    localStorage.removeItem('error_reports')
  }
}

export default ErrorBoundary

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook to manually report errors
export function useErrorReporting() {
  const reportError = (error: Error, context?: string) => {
    const errorReport: ErrorReport = {
      errorId: `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      buildVersion: process.env['VITE_BUILD_VERSION'] || 'unknown'
    }

    // Add context if provided
    if (context) {
      errorReport.message = `${context}: ${errorReport.message}`
    }

    // Store and report
    try {
      const existingReports = JSON.parse(localStorage.getItem('error_reports') || '[]')
      existingReports.push(errorReport)
      localStorage.setItem('error_reports', JSON.stringify(existingReports.slice(-50)))
    } catch (e) {
      console.error('Failed to store error report:', e)
    }

    console.error('Manual error report:', errorReport)
  }

  return { reportError }
}