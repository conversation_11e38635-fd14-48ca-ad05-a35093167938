import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'jotai'
import { createStore } from 'jotai'
import { ApiKeyCard } from '../../user/ApiKeyCard'
import type { ApiKey } from '../../types/apiKey'

const mockApiKey: ApiKey = {
  id: 'key-1',
  userId: 'user-1',
  name: 'Production Key',
  description: 'Key for production use',
  keyHash: 'hash-1',
  status: 'active',
  permissions: [{ resource: 'ollama', actions: ['read', 'write'] }],
  rateLimit: {
    requestsPerMinute: 60,
    requestsPerHour: 1000,
    requestsPerDay: 10000
  },
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  lastUsedAt: new Date('2024-01-02')
}

const mockExpiredApiKey: ApiKey = {
  ...mockApiKey,
  id: 'key-2',
  name: 'Expired Key',
  expiresAt: new Date('2023-12-31') // Expired
}

const mockRevokedApiKey: ApiKey = {
  ...mockApiKey,
  id: 'key-3',
  name: 'Revoked Key',
  status: 'revoked'
}

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createStore()
  return <Provider store={store}>{children}</Provider>
}

describe('ApiKeyCard', () => {
  const mockOnRegenerate = vi.fn()
  const mockOnRevoke = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnViewDetails = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render API key information correctly', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    expect(screen.getByText('Production Key')).toBeInTheDocument()
    expect(screen.getByText('Key for production use')).toBeInTheDocument()
    expect(screen.getByText(/active/i)).toBeInTheDocument()
    expect(screen.getByText(/created on/i)).toBeInTheDocument()
    expect(screen.getByText(/last used/i)).toBeInTheDocument()
  })

  it('should show correct status badge for active key', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const statusBadge = screen.getByText(/active/i)
    expect(statusBadge).toHaveClass('bg-green-100', 'text-green-800')
  })

  it('should show correct status badge for revoked key', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockRevokedApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const statusBadge = screen.getByText(/revoked/i)
    expect(statusBadge).toHaveClass('bg-red-100', 'text-red-800')
  })

  it('should show expiry warning for expired key', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockExpiredApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/expired/i)).toBeInTheDocument()
    expect(screen.getByTestId('warning-icon')).toBeInTheDocument()
  })

  it('should show rate limit information', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/60.*per minute/i)).toBeInTheDocument()
    expect(screen.getByText(/1,000.*per hour/i)).toBeInTheDocument()
    expect(screen.getByText(/10,000.*per day/i)).toBeInTheDocument()
  })

  it('should show permissions information', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/ollama/i)).toBeInTheDocument()
    expect(screen.getByText(/read/i)).toBeInTheDocument()
    expect(screen.getByText(/write/i)).toBeInTheDocument()
  })

  it('should call onViewDetails when card is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const card = screen.getByRole('button', { name: /view details/i })
    await user.click(card)

    expect(mockOnViewDetails).toHaveBeenCalledWith(mockApiKey)
  })

  it('should call onRegenerate when regenerate button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const regenerateButton = screen.getByRole('button', { name: /regenerate/i })
    await user.click(regenerateButton)

    expect(mockOnRegenerate).toHaveBeenCalledWith(mockApiKey.id)
  })

  it('should call onRevoke when revoke button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const revokeButton = screen.getByRole('button', { name: /revoke/i })
    await user.click(revokeButton)

    expect(mockOnRevoke).toHaveBeenCalledWith(mockApiKey.id)
  })

  it('should call onDelete when delete button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    await user.click(deleteButton)

    expect(mockOnDelete).toHaveBeenCalledWith(mockApiKey.id)
  })

  it('should disable action buttons for revoked keys', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockRevokedApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const regenerateButton = screen.getByRole('button', { name: /regenerate/i })
    const revokeButton = screen.queryByRole('button', { name: /revoke/i })

    expect(regenerateButton).toBeDisabled()
    expect(revokeButton).not.toBeInTheDocument() // Should not show revoke for already revoked key
  })

  it('should show confirmation dialog before destructive actions', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    await user.click(deleteButton)

    expect(screen.getByText(/are you sure/i)).toBeInTheDocument()
    expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA labels', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const card = screen.getByRole('button', { name: /view details/i })
    expect(card).toHaveAttribute('aria-label', expect.stringContaining('Production Key'))

    const regenerateButton = screen.getByRole('button', { name: /regenerate/i })
    expect(regenerateButton).toHaveAttribute('aria-label', expect.stringContaining('Regenerate'))

    const revokeButton = screen.getByRole('button', { name: /revoke/i })
    expect(revokeButton).toHaveAttribute('aria-label', expect.stringContaining('Revoke'))

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    expect(deleteButton).toHaveAttribute('aria-label', expect.stringContaining('Delete'))
  })

  it('should handle keyboard navigation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    // Tab through interactive elements
    await user.tab()
    expect(screen.getByRole('button', { name: /view details/i })).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('button', { name: /regenerate/i })).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('button', { name: /revoke/i })).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('button', { name: /delete/i })).toHaveFocus()
  })

  it('should handle Enter key activation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    const card = screen.getByRole('button', { name: /view details/i })
    card.focus()
    await user.keyboard('{Enter}')

    expect(mockOnViewDetails).toHaveBeenCalledWith(mockApiKey)
  })

  it('should format dates correctly', () => {
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={mockApiKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    // Check that dates are formatted in a readable way
    expect(screen.getByText(/Jan 1, 2024/i)).toBeInTheDocument() // Created date
    expect(screen.getByText(/Jan 2, 2024/i)).toBeInTheDocument() // Last used date
  })

  it('should show "Never used" when lastUsedAt is null', () => {
    const neverUsedKey = { ...mockApiKey, lastUsedAt: undefined }
    
    render(
      <TestWrapper>
        <ApiKeyCard
          apiKey={neverUsedKey}
          onRegenerate={mockOnRegenerate}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/never used/i)).toBeInTheDocument()
  })
})