import React, { useState } from 'react'
import { useAtomValue } from 'jotai'
import { userAtom } from '../../stores/auth'
import { Outlet, Link, useLocation } from 'react-router-dom'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Sheet,
  SheetContent,
  SheetTrigger
} from '../ui/sheet'
import { 
  Users, 
  BarChart3, 
  Settings, 
  Home,
  Menu,
  Shield,
  Activity,
  Key,
  AlertTriangle,
  LogOut,
  User
} from 'lucide-react'
import { cn } from '../../lib/utils'

interface AdminLayoutProps {
  children?: React.ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const user = useAtomValue(userAtom)
  const location = useLocation()

  const navigationItems = [
    {
      title: 'Dashboard',
      href: '/admin',
      icon: Home,
      description: 'System overview'
    },
    {
      title: 'User Management',
      href: '/admin/users',
      icon: Users,
      description: 'Manage user accounts',
      badge: '3' // Could be dynamic based on pending approvals
    },
    {
      title: 'API Usage',
      href: '/admin/usage',
      icon: BarChart3,
      description: 'Monitor API usage'
    },
    {
      title: 'Performance',
      href: '/admin/performance',
      icon: Activity,
      description: 'System performance'
    },
    {
      title: 'API Keys',
      href: '/admin/keys',
      icon: Key,
      description: 'Manage API keys'
    },
    {
      title: 'Errors & Logs',
      href: '/admin/errors',
      icon: AlertTriangle,
      description: 'Error monitoring'
    },
    {
      title: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      description: 'System settings'
    }
  ]

  const isActiveRoute = (href: string) => {
    if (href === '/admin') {
      return location.pathname === '/admin'
    }
    return location.pathname.startsWith(href)
  }

  const NavigationContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="font-semibold">Admin Panel</h2>
            <p className="text-sm text-muted-foreground">Home Server</p>
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon
          const isActive = isActiveRoute(item.href)
          
          return (
            <Link
              key={item.href}
              to={item.href}
              onClick={() => setIsMobileMenuOpen(false)}
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted"
              )}
            >
              <Icon className="h-4 w-4" />
              <div className="flex-1">
                <div className="font-medium">{item.title}</div>
                <div className="text-xs opacity-75">{item.description}</div>
              </div>
              {item.badge && (
                <Badge variant={isActive ? "secondary" : "outline-solid"} className="text-xs">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )
        })}
      </nav>

      {/* User Info */}
      <div className="p-4 border-t">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
          <div className="p-1.5 bg-primary/10 rounded-full">
            <User className="h-4 w-4 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm truncate">{user?.name}</div>
            <div className="text-xs text-muted-foreground truncate">{user?.email}</div>
          </div>
          <Badge variant="admin" className="text-xs">
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </Badge>
        </div>
        
        <Button 
          variant="ghost" 
          size="sm" 
          className="w-full mt-2 justify-start text-muted-foreground"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r bg-background">
          <NavigationContent />
        </div>
      </div>

      {/* Mobile Menu */}
      <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden fixed top-4 left-4 z-40"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-72 p-0">
          <NavigationContent />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="lg:pl-72">
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b bg-background px-4 shadow-xs sm:gap-x-6 sm:px-6 lg:px-8">
          {/* Mobile menu button space */}
          <div className="lg:hidden w-10" />
          
          {/* Breadcrumb or page title could go here */}
          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Additional header content */}
            </div>
          </div>
        </div>

        <main className="py-6">
          <div className="px-4 sm:px-6 lg:px-8">
            {children || <Outlet />}
          </div>
        </main>
      </div>
    </div>
  )
}