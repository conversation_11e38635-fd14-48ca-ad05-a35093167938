import React from 'react'
import { Outlet } from 'react-router-dom'
import { AdminLayout } from './AdminLayout'
import { UserLayout } from './UserLayout'
import { MobileNavigation } from './MobileNavigation'

interface ResponsiveLayoutProps {
  isAdmin?: boolean
}

export function ResponsiveLayout({ isAdmin = false }: ResponsiveLayoutProps) {
  return (
    <>
      {/* Mobile navigation - only visible on mobile */}
      <MobileNavigation isAdmin={isAdmin} />
      
      {/* Desktop layout - hidden on mobile */}
      <div className="hidden md:block">
        {isAdmin ? <AdminLayout /> : <UserLayout />}
      </div>
      
      {/* Mobile content - only visible on mobile */}
      <div className="md:hidden">
        <main className="pt-16 p-4">
          <Outlet />
        </main>
      </div>
    </>
  )
}