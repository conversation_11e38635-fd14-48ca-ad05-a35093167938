import { BaseLineChart } from './BaseLineChart'
import { ChartData, ChartConfig } from '@/types/charts'
import { TrendData } from '@/types/analytics'
import { chartTheme } from '@/lib/chart-theme'
import { format } from 'date-fns'

interface ApiUsageOverTimeChartProps {
  data: TrendData[]
  title?: string
  className?: string
  loading?: boolean
  error?: string
  height?: number
  showMultipleMetrics?: boolean
  additionalMetrics?: {
    responseTime?: TrendData[]
    errors?: TrendData[]
  }
}

export function ApiUsageOverTimeChart({
  data,
  title = 'API Usage Over Time',
  className,
  loading = false,
  error,
  height = 300,
  showMultipleMetrics = false,
  additionalMetrics,
}: ApiUsageOverTimeChartProps) {
  // Transform data for chart consumption
  const chartData: ChartData[] = data.map((item, index) => {
    const baseData: ChartData = {
      timestamp: format(item.timestamp, 'MMM dd, HH:mm'),
      requests: item.value,
    }

    if (showMultipleMetrics && additionalMetrics) {
      if (additionalMetrics.responseTime?.[index]) {
        baseData.responseTime = additionalMetrics.responseTime[index].value
      }
      if (additionalMetrics.errors?.[index]) {
        baseData.errors = additionalMetrics.errors[index].value
      }
    }

    return baseData
  })

  const dataKeys = [
    {
      key: 'requests',
      name: 'API Requests',
      color: chartTheme.colors.primary,
      type: 'monotone' as const,
    },
  ]

  if (showMultipleMetrics && additionalMetrics) {
    if (additionalMetrics.responseTime) {
      dataKeys.push({
        key: 'responseTime',
        name: 'Avg Response Time (ms)',
        color: chartTheme.colors.warning,
        type: 'monotone' as const,
      })
    }
    if (additionalMetrics.errors) {
      dataKeys.push({
        key: 'errors',
        name: 'Error Count',
        color: chartTheme.colors.error,
        type: 'monotone' as const,
      })
    }
  }

  const config: ChartConfig = {
    title,
    xAxisKey: 'timestamp',
    dataKeys,
    showGrid: true,
    showLegend: showMultipleMetrics,
    showTooltip: true,
    height,
    margin: { top: 5, right: 30, left: 20, bottom: 5 },
  }

  return (
    <BaseLineChart
      data={chartData}
      config={config}
      className={className}
      loading={loading}
      error={error}
      smooth={true}
    />
  )
}