import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,

} from 'recharts'
import { ChartContainer } from './ChartContainer'
import { chartTheme } from '@/lib/chart-theme'
import type { BaseChartProps } from '@/types/charts'

interface BaseLineChartProps extends BaseChartProps {
  smooth?: boolean
}

export function BaseLineChart({
  data,
  config,
  className,
  loading = false,
  error,
  smooth = true,
}: BaseLineChartProps) {
  const {
    title,
    xAxisKey,
    dataKeys,
    showGrid = true,
    showLegend = true,
    showTooltip = true,
    height = 300,
    margin = { top: 5, right: 30, left: 20, bottom: 5 },
  } = config

  return (
    <ChartContainer
      title={title}
      className={className}
      loading={loading}
      error={error}
      height={height}
    >
      <LineChart data={data} margin={margin}>
        {showGrid && (
          <CartesianGrid
            strokeDasharray={chartTheme.grid.strokeDasharray}
            stroke={chartTheme.grid.stroke}
            strokeOpacity={chartTheme.grid.strokeOpacity}
          />
        )}
        <XAxis
          dataKey={xAxisKey}
          stroke={chartTheme.axis.stroke}
          fontSize={chartTheme.axis.fontSize}
          fontFamily={chartTheme.axis.fontFamily}
          fill={chartTheme.axis.fill}
        />
        <YAxis
          stroke={chartTheme.axis.stroke}
          fontSize={chartTheme.axis.fontSize}
          fontFamily={chartTheme.axis.fontFamily}
          fill={chartTheme.axis.fill}
        />
        {showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: chartTheme.tooltip.backgroundColor,
              border: chartTheme.tooltip.border,
              borderRadius: chartTheme.tooltip.borderRadius,
              boxShadow: chartTheme.tooltip.boxShadow,
              color: chartTheme.tooltip.color,
              fontSize: chartTheme.tooltip.fontSize,
              fontFamily: chartTheme.tooltip.fontFamily,
            }}
          />
        )}
        {showLegend && (
          <Legend
            wrapperStyle={{
              fontSize: chartTheme.legend.fontSize,
              fontFamily: chartTheme.legend.fontFamily,
              fill: chartTheme.legend.fill,
            }}
          />
        )}
        {dataKeys.map((dataKey) => (
          <Line
            key={dataKey.key}
            type={smooth ? (dataKey.type || 'monotone') : 'linear'}
            dataKey={dataKey.key}
            stroke={dataKey.color}
            strokeWidth={2}
            name={dataKey.name}
            dot={{ fill: dataKey.color, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: dataKey.color, strokeWidth: 2 }}
          />
        ))}
      </LineChart>
    </ChartContainer>
  )
}