
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { X, Search, Filter } from 'lucide-react'
import type { UserFilters as UserFiltersType } from '../../types/user'

interface UserFiltersProps {
  filters: UserFiltersType
  onFiltersChange: (filters: Partial<UserFiltersType>) => void
}

export function UserFilters({ filters, onFiltersChange }: UserFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value as UserFiltersType['status'] })
  }

  const handleRoleChange = (value: string) => {
    onFiltersChange({ role: value as UserFiltersType['role'] })
  }

  const handleAccessLevelChange = (value: string) => {
    onFiltersChange({ apiAccessLevel: value as UserFiltersType['apiAccessLevel'] })
  }

  const clearFilters = () => {
    onFiltersChange({
      status: 'all',
      role: 'all',
      apiAccessLevel: 'all',
      search: '',
      dateRange: undefined
    })
  }

  const hasActiveFilters = 
    filters.status !== 'all' ||
    filters.role !== 'all' ||
    filters.apiAccessLevel !== 'all' ||
    filters.search ||
    filters.dateRange

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.status !== 'all') count++
    if (filters.role !== 'all') count++
    if (filters.apiAccessLevel !== 'all') count++
    if (filters.search) count++
    if (filters.dateRange) count++
    return count
  }

  return (
    <div className="space-y-4">
      {/* Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search users by name or email..."
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Status Filter */}
        <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="revoked">Revoked</SelectItem>
          </SelectContent>
        </Select>

        {/* Role Filter */}
        <Select value={filters.role || 'all'} onValueChange={handleRoleChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="user">User</SelectItem>
          </SelectContent>
        </Select>

        {/* Access Level Filter */}
        <Select value={filters.apiAccessLevel || 'all'} onValueChange={handleAccessLevelChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by access" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Access Levels</SelectItem>
            <SelectItem value="none">No Access</SelectItem>
            <SelectItem value="basic">Basic</SelectItem>
            <SelectItem value="premium">Premium</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Active Filters and Clear */}
      {hasActiveFilters && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-1">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Active filters:</span>
            </div>
            
            {filters.status !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Status: {filters.status}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleStatusChange('all')}
                />
              </Badge>
            )}
            
            {filters.role !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Role: {filters.role}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleRoleChange('all')}
                />
              </Badge>
            )}
            
            {filters.apiAccessLevel !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Access: {filters.apiAccessLevel}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleAccessLevelChange('all')}
                />
              </Badge>
            )}
            
            {filters.search && (
              <Badge variant="secondary" className="gap-1">
                Search: "{filters.search}"
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleSearchChange('')}
                />
              </Badge>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all ({getActiveFilterCount()})
          </Button>
        </div>
      )}
    </div>
  )
}