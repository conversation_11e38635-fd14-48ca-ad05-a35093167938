
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Zap,
  Target
} from 'lucide-react'

interface PerformanceMetricsCardProps {
  metrics: {
    p50ResponseTime: number
    p95ResponseTime: number
    p99ResponseTime: number
    slowestEndpoints: Array<{ endpoint: string; averageResponseTime: number; count: number }>
    fastestEndpoints: Array<{ endpoint: string; averageResponseTime: number; count: number }>
  }
  isLoading: boolean
}

export function PerformanceMetricsCard({ metrics, isLoading }: PerformanceMetricsCardProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-muted animate-pulse rounded w-1/3" />
                <div className="h-2 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const getPerformanceColor = (responseTime: number) => {
    if (responseTime < 200) return 'text-green-600'
    if (responseTime < 500) return 'text-yellow-600'
    if (responseTime < 1000) return 'text-orange-600'
    return 'text-red-600'
  }

  const getPerformanceVariant = (responseTime: number) => {
    if (responseTime < 200) return 'success'
    if (responseTime < 500) return 'warning'
    return 'destructive'
  }

  const getProgressValue = (responseTime: number, max = 2000) => {
    return Math.min((responseTime / max) * 100, 100)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Performance Metrics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Response Time Percentiles */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Response Time Percentiles</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">P50 (Median)</span>
              </div>
              <Badge variant={getPerformanceVariant(metrics.p50ResponseTime)}>
                {metrics.p50ResponseTime}ms
              </Badge>
            </div>
            <Progress 
              value={getProgressValue(metrics.p50ResponseTime)} 
              className="h-2"
            />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">P95</span>
              </div>
              <Badge variant={getPerformanceVariant(metrics.p95ResponseTime)}>
                {metrics.p95ResponseTime}ms
              </Badge>
            </div>
            <Progress 
              value={getProgressValue(metrics.p95ResponseTime)} 
              className="h-2"
            />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">P99</span>
              </div>
              <Badge variant={getPerformanceVariant(metrics.p99ResponseTime)}>
                {metrics.p99ResponseTime}ms
              </Badge>
            </div>
            <Progress 
              value={getProgressValue(metrics.p99ResponseTime)} 
              className="h-2"
            />
          </div>
        </div>

        {/* Fastest Endpoints */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Zap className="h-4 w-4 text-green-600" />
            Fastest Endpoints
          </h4>
          <div className="space-y-2">
            {metrics.fastestEndpoints.slice(0, 3).map((endpoint, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="font-mono text-xs truncate max-w-[200px]">
                  {endpoint.endpoint}
                </span>
                <div className="flex items-center gap-2">
                  <span className={`font-medium ${getPerformanceColor(endpoint.averageResponseTime)}`}>
                    {endpoint.averageResponseTime}ms
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({endpoint.count} req)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Slowest Endpoints */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-red-600" />
            Slowest Endpoints
          </h4>
          <div className="space-y-2">
            {metrics.slowestEndpoints.slice(0, 3).map((endpoint, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="font-mono text-xs truncate max-w-[200px]">
                  {endpoint.endpoint}
                </span>
                <div className="flex items-center gap-2">
                  <span className={`font-medium ${getPerformanceColor(endpoint.averageResponseTime)}`}>
                    {endpoint.averageResponseTime}ms
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({endpoint.count} req)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Summary */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {metrics.fastestEndpoints.length}
              </div>
              <div className="text-xs text-muted-foreground">Fast Endpoints</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {metrics.slowestEndpoints.length}
              </div>
              <div className="text-xs text-muted-foreground">Slow Endpoints</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}