import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Database, 
  Users,
  Globe,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface ApiUsageStatsCardsProps {
  stats: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    totalDataTransferred: number
    uniqueUsers: number
    uniqueEndpoints: number
    errorRate: number
    successRate: number
  }
  isLoading: boolean
}

export function ApiUsageStatsCards({ stats, isLoading }: ApiUsageStatsCardsProps) {
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const cards = [
    {
      title: 'Total Requests',
      value: formatNumber(stats.totalRequests),
      icon: Activity,
      description: 'All API requests',
      color: 'text-blue-600'
    },
    {
      title: 'Successful Requests',
      value: formatNumber(stats.successfulRequests),
      icon: CheckCircle,
      description: `${(stats.successRate * 100).toFixed(1)}% success rate`,
      color: 'text-green-600',
      trend: stats.successRate > 0.95 ? 'up' : stats.successRate < 0.9 ? 'down' : undefined
    },
    {
      title: 'Failed Requests',
      value: formatNumber(stats.failedRequests),
      icon: XCircle,
      description: `${(stats.errorRate * 100).toFixed(1)}% error rate`,
      color: 'text-red-600',
      badge: stats.failedRequests > 0 ? 'destructive' : undefined,
      trend: stats.errorRate > 0.05 ? 'up' : stats.errorRate < 0.01 ? 'down' : undefined
    },
    {
      title: 'Avg Response Time',
      value: `${stats.averageResponseTime}ms`,
      icon: Clock,
      description: 'Average response time',
      color: 'text-orange-600',
      trend: stats.averageResponseTime < 200 ? 'down' : stats.averageResponseTime > 500 ? 'up' : undefined
    },
    {
      title: 'Data Transferred',
      value: formatBytes(stats.totalDataTransferred),
      icon: Database,
      description: 'Total data transferred',
      color: 'text-purple-600'
    },
    {
      title: 'Active Users',
      value: stats.uniqueUsers,
      icon: Users,
      description: 'Unique users',
      color: 'text-indigo-600'
    },
    {
      title: 'Endpoints Used',
      value: stats.uniqueEndpoints,
      icon: Globe,
      description: 'Unique endpoints',
      color: 'text-teal-600'
    },
    {
      title: 'System Health',
      value: stats.successRate > 0.95 ? 'Excellent' : stats.successRate > 0.9 ? 'Good' : 'Poor',
      icon: Activity,
      description: 'Overall system health',
      color: stats.successRate > 0.95 ? 'text-green-600' : stats.successRate > 0.9 ? 'text-yellow-600' : 'text-red-600',
      badge: stats.successRate > 0.95 ? 'success' : stats.successRate > 0.9 ? 'warning' : 'destructive'
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => {
        const Icon = card.icon
        const TrendIcon = card.trend === 'up' ? TrendingUp : card.trend === 'down' ? TrendingDown : null
        
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className="flex items-center gap-2">
                {card.badge && (
                  <Badge variant={card.badge as any} className="text-xs">
                    {typeof card.value === 'string' ? card.value : formatNumber(card.value)}
                  </Badge>
                )}
                {TrendIcon && (
                  <TrendIcon className={`h-3 w-3 ${
                    card.trend === 'up' ? 'text-red-500' : 'text-green-500'
                  }`} />
                )}
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}