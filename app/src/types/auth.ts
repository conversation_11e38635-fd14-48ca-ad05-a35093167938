export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'user'
  status: 'pending' | 'approved' | 'revoked'
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  apiAccessLevel: 'none' | 'basic' | 'premium'
  // Additional profile fields
  bio?: string
  company?: string
  location?: string
  website?: string
  preferences?: {
    emailNotifications: boolean
    securityAlerts: boolean
    loginNotifications: boolean
    apiKeyAlerts: boolean
    twoFactorEnabled: boolean
  }
}

export interface Session {
  id: string
  userId: string
  user: User
  token: string
  expiresAt: Date
  createdAt: Date
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
}

export interface AuthState {
  session: Session | null
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface AuthError {
  code: string
  message: string
  details?: Record<string, any>
}