import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { authClient } from '../lib/auth'
import type { Session, User, LoginCredentials, SignupData, AuthError } from '../types/auth'

// Base atoms for authentication state
export const sessionAtom = atomWithStorage<Session | null>('auth-session', null)
export const isLoadingAtom = atom<boolean>(false)
export const authErrorAtom = atom<AuthError | null>(null)

// Derived atoms
export const isAuthenticatedAtom = atom((get) => {
  const session = get(sessionAtom)
  return session !== null && new Date(session.expiresAt) > new Date()
})

export const userAtom = atom((get) => {
  const session = get(sessionAtom)
  return session?.user || null
})

export const userRoleAtom = atom((get) => {
  const user = get(userAtom)
  return user?.role || 'user'
})

export const isAdminAtom = atom((get) => {
  const role = get(userR<PERSON><PERSON>tom)
  return role === 'admin'
})

export const userStatusAtom = atom((get) => {
  const user = get(userAtom)
  return user?.status || 'pending'
})

export const canAccessApiAtom = atom((get) => {
  const status = get(userStatusAtom)
  return status === 'approved'
})

// Action atoms for authentication operations
export const loginAtom = atom(
  null,
  async (_get, set, credentials: LoginCredentials) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.signIn.email({
        email: credentials.email,
        password: credentials.password,
      })

      if (response.data) {
        const session: Session = {
          id: 'session-' + response.data.user.id,
          userId: response.data.user.id,
          user: response.data.user as User,
          token: response.data.token,
          expiresAt: new Date(response.data.expiresAt),
          createdAt: new Date(),
        }

        set(sessionAtom, session)
        return session
      } else {
        throw new Error(response.error?.message || 'Login failed')
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'LOGIN_FAILED',
        message: error instanceof Error ? error.message : 'Login failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const signupAtom = atom(
  null,
  async (_get, set, signupData: SignupData) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.signUp.email({
        email: signupData.email,
        password: signupData.password,
        name: signupData.name,
      })

      if (response.data) {
        const session: Session = {
          id: 'session-' + response.data.user.id,
          userId: response.data.user.id,
          user: response.data.user as User,
          token: response.data.token,
          expiresAt: new Date(response.data.expiresAt),
          createdAt: new Date(),
        }

        set(sessionAtom, session)
        return session
      } else {
        throw new Error(response.error?.message || 'Signup failed')
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'SIGNUP_FAILED',
        message: error instanceof Error ? error.message : 'Signup failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const logoutAtom = atom(
  null,
  async (_get, set) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      await authClient.signOut()
      set(sessionAtom, null)
    } catch (error) {
      const authError: AuthError = {
        code: 'LOGOUT_FAILED',
        message: error instanceof Error ? error.message : 'Logout failed',
      }
      set(authErrorAtom, authError)
      // Still clear session even if logout request fails
      set(sessionAtom, null)
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const refreshSessionAtom = atom(
  null,
  async (get, set) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) return null

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.getSession()

      if (response.data) {
        const session: Session = {
          id: 'session-' + response.data.user.id,
          userId: response.data.user.id,
          user: response.data.user as User,
          token: response.data.token,
          expiresAt: new Date(response.data.expiresAt),
          createdAt: new Date(),
        }

        set(sessionAtom, session)
        return session
      } else {
        // Session is invalid, clear it
        set(sessionAtom, null)
        return null
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'SESSION_REFRESH_FAILED',
        message: error instanceof Error ? error.message : 'Session refresh failed',
      }
      set(authErrorAtom, authError)
      set(sessionAtom, null)
      return null
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Utility atom to check if session needs refresh
export const shouldRefreshSessionAtom = atom((get) => {
  const session = get(sessionAtom)
  if (!session) return false

  const now = new Date()
  const expiresAt = new Date(session.expiresAt)
  const timeUntilExpiry = expiresAt.getTime() - now.getTime()

  // Refresh if session expires in less than 5 minutes
  return timeUntilExpiry < 5 * 60 * 1000
})

// Update user profile atom
export const updateUserProfileAtom = atom(
  null,
  async (get, set, profileData: Partial<User>) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      const updatedUser = { ...currentSession.user, ...profileData, updatedAt: new Date() }
      const updatedSession = { ...currentSession, user: updatedUser }
      
      set(sessionAtom, updatedSession)
      return updatedUser
    } catch (error) {
      const authError: AuthError = {
        code: 'PROFILE_UPDATE_FAILED',
        message: error instanceof Error ? error.message : 'Profile update failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Change password atom
export const changePasswordAtom = atom(
  null,
  async (get, set, passwordData: { currentPassword: string; newPassword: string }) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      // In a real implementation, this would call the backend to change the password
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay
      
      return { success: true }
    } catch (error) {
      const authError: AuthError = {
        code: 'PASSWORD_CHANGE_FAILED',
        message: error instanceof Error ? error.message : 'Password change failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Update user preferences atom
export const updateUserPreferencesAtom = atom(
  null,
  async (get, set, preferences: any) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      const updatedUser = { 
        ...currentSession.user, 
        preferences: { ...currentSession.user.preferences, ...preferences },
        updatedAt: new Date() 
      }
      const updatedSession = { ...currentSession, user: updatedUser }
      
      set(sessionAtom, updatedSession)
      return updatedUser
    } catch (error) {
      const authError: AuthError = {
        code: 'PREFERENCES_UPDATE_FAILED',
        message: error instanceof Error ? error.message : 'Preferences update failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Clear error atom
export const clearAuthErrorAtom = atom(
  null,
  (_get, set) => {
    set(authErrorAtom, null)
  }
)