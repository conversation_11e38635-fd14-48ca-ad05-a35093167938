@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base shadcn/ui CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;

  /* Admin panel specific variables */
  --admin: 221.2 83.2% 53.3%;
  --admin-foreground: 210 40% 98%;
  --admin-hover: 221.2 83.2% 48%;
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;
  --warning: 32.5 94.6% 43.7%;
  --warning-foreground: 355.7 100% 97.3%;
  --info: 204.4 94% 53.9%;
  --info-foreground: 355.7 100% 97.3%;

  /* Layout variables */
  --sidebar-width: 16rem;
  --header-height: 4rem;
  --content-padding: 1.5rem;
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --header: 0 0% 100%;
  --header-foreground: 222.2 84% 4.9%;
  --header-border: 214.3 31.8% 91.4%;

  /* Shadows */
  --admin-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --card-hover-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --sidebar-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;

  /* Admin panel dark mode */
  --admin: 217.2 91.2% 59.8%;
  --admin-foreground: 222.2 84% 4.9%;
  --admin-hover: 217.2 91.2% 54%;
  --sidebar: 222.2 84% 4.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --header: 222.2 84% 4.9%;
  --header-foreground: 210 40% 98%;
  --header-border: 217.2 32.6% 17.5%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}