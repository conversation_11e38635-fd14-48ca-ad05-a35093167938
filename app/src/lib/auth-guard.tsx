import type { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAtomValue } from 'jotai'
import { sessionAtom, isAuthenticatedAtom, userRoleAtom } from '../stores/auth'

interface AuthGuardProps {
  children: ReactNode
  requiredRole?: 'admin' | 'user'
  fallback?: ReactNode
}

export function AuthGuard({ children, requiredRole, fallback }: AuthGuardProps) {
  const location = useLocation()
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const userRole = useAtomValue(userRoleAtom)
  const session = useAtomValue(sessionAtom)

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // If user is not approved, show pending status
  if (session?.user?.status === 'pending') {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-4">Account Pending Approval</h2>
            <p className="text-gray-600">
              Your account is pending approval from an administrator. 
              Please wait for approval to access the system.
            </p>
          </div>
        </div>
      )
    )
  }

  // If user is revoked, show access denied
  if (session?.user?.status === 'revoked') {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-4">Access Revoked</h2>
            <p className="text-gray-600">
              Your access has been revoked. Please contact an administrator.
            </p>
          </div>
        </div>
      )
    )
  }

  // Check role-based access
  if (requiredRole && userRole !== requiredRole) {
    // If admin role required but user is not admin, deny access
    if (requiredRole === 'admin' && userRole !== 'admin') {
      return <Navigate to="/dashboard" replace />
    }
  }

  return <>{children}</>
}

interface PublicRouteProps {
  children: ReactNode
}

export function PublicRoute({ children }: PublicRouteProps) {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const userRole = useAtomValue(userRoleAtom)

  // If authenticated, redirect to appropriate dashboard
  if (isAuthenticated) {
    const redirectPath = userRole === 'admin' ? '/admin/dashboard' : '/dashboard'
    return <Navigate to={redirectPath} replace />
  }

  return <>{children}</>
}