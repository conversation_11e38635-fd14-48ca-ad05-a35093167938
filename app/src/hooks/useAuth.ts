import { useAtomValue, useSetAtom } from 'jotai'
import { useCallback, useEffect } from 'react'
import {
  sessionAtom,
  isAuthenticated<PERSON>tom,
  user<PERSON>tom,
  userRole<PERSON>tom,
  isAdmin<PERSON>tom,
  userStatus<PERSON>tom,
  canAccessApi<PERSON>tom,
  isLoading<PERSON>tom,
  authErrorAtom,
  login<PERSON>tom,
  signup<PERSON>tom,
  logoutAtom,
  refreshSessionAtom,
  shouldRefreshSessionAtom,
  clearAuthErrorAtom,
} from '../stores/auth'
import type { LoginCredentials, SignupData } from '../types/auth'

export function useAuth() {
  const session = useAtomValue(sessionAtom)
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const user = useAtomValue(userAtom)
  const userRole = useAtomValue(userRoleAtom)
  const isAdmin = useAtomValue(isAdminAtom)
  const userStatus = useAtomValue(userStatusAtom)
  const canAccessApi = useAtomValue(canAccessApi<PERSON>tom)
  const isLoading = useAtomValue(isLoadingAtom)
  const error = useAtomValue(authErrorAtom)
  const shouldRefreshSession = useAtomValue(shouldRefreshSessionAtom)

  const login = useSetAtom(loginAtom)
  const signup = useSetAtom(signupAtom)
  const logout = useSetAtom(logoutAtom)
  const refreshSession = useSetAtom(refreshSessionAtom)
  const clearError = useSetAtom(clearAuthErrorAtom)

  // Auto-refresh session when needed
  useEffect(() => {
    if (shouldRefreshSession && isAuthenticated) {
      refreshSession()
    }
  }, [shouldRefreshSession, isAuthenticated, refreshSession])

  // Periodic session check (every 5 minutes)
  useEffect(() => {
    if (!isAuthenticated) return

    const interval = setInterval(() => {
      refreshSession()
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearInterval(interval)
  }, [isAuthenticated, refreshSession])

  const handleLogin = useCallback(async (credentials: LoginCredentials) => {
    try {
      const session = await login(credentials)
      return session
    } catch (error) {
      throw error
    }
  }, [login])

  const handleSignup = useCallback(async (signupData: SignupData) => {
    try {
      const session = await signup(signupData)
      return session
    } catch (error) {
      throw error
    }
  }, [signup])

  const handleLogout = useCallback(async () => {
    try {
      await logout()
    } catch (error) {
      // Logout should always succeed locally even if server request fails
      console.error('Logout error:', error)
    }
  }, [logout])

  return {
    // State
    session,
    user,
    isAuthenticated,
    userRole,
    isAdmin,
    userStatus,
    canAccessApi,
    isLoading,
    error,
    
    // Actions
    login: handleLogin,
    signup: handleSignup,
    logout: handleLogout,
    refreshSession,
    clearError,
  }
}

export function useAuthGuard(requiredRole?: 'admin' | 'user') {
  const { isAuthenticated, userRole, userStatus } = useAuth()

  const canAccess = useCallback(() => {
    if (!isAuthenticated) return false
    if (userStatus !== 'approved') return false
    if (requiredRole && userRole !== requiredRole) {
      // Admin can access user routes, but user cannot access admin routes
      if (requiredRole === 'admin' && userRole !== 'admin') return false
    }
    return true
  }, [isAuthenticated, userRole, userStatus, requiredRole])

  return {
    canAccess: canAccess(),
    isAuthenticated,
    userRole,
    userStatus,
  }
}