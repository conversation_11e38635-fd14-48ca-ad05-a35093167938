import { betterAuth } from "better-auth";
import { admin as adminPlugin } from "better-auth/plugins"
import type { Db } from "mongodb";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { createAccessControl } from "better-auth/plugins/access";

export const statement = {
	apiKey: ["create", "read", "update", "delete", "admin"],
} as const;

const ac = createAccessControl(statement);

const whiteListed = ac.newRole({
	apiKey: ["create", "read", "update", "delete"],
})

const user = ac.newRole({
	apiKey: [],
});

const admin = ac.newRole({
	apiKey: ["create", "read", "update", "delete", "admin"],
});

class AuthManager {
	private static instance: AuthManager | null = null;
	private authInstance: ReturnType<typeof betterAuth> | null = null;
	private initialized = false;

	private constructor() {
		// Private constructor to prevent direct construction calls with 'new'
	}

	/**
	 * Get the singleton instance of AuthManager
	 */
	public static getInstance(): AuthManager {
		if (!AuthManager.instance) {
			AuthManager.instance = new AuthManager();
		}
		return AuthManager.instance;
	}

	/**
	 * Initialize the auth instance with database connection
	 * @param db MongoDB database instance
	 * @returns The initialized auth instance
	 */
	public initialize(db: Db): ReturnType<typeof betterAuth> {
		if (this.initialized && this.authInstance) {
			console.log("🔄 Using existing auth instance");
			return this.authInstance;
		}

		console.log("🔐 Initializing auth instance...");
		this.authInstance = betterAuth({
			database: mongodbAdapter(db),
			secret: process.env.BETTER_AUTH_SECRET || "super secure secret",
			emailAndPassword: {
				enabled: true, // If you want to use email and password auth
			},
			trustedOrigins: ["https://home-server.kvmanas.com"],
			plugins: [adminPlugin({
				ac,
				roles: {
					whiteListed,
					user,
					admin,
				},
			})]
		});


		this.initialized = true;
		console.log("✅ Auth instance initialized successfully");
		return this.authInstance;
	}

	/**
	 * Get the auth instance, throws error if not initialized
	 */
	public getAuth(): ReturnType<typeof betterAuth> {
		if (!this.initialized || !this.authInstance) {
			throw new Error(
				"Auth is not initialized. Call initialize() with database instance first.",
			);
		}
		return this.authInstance;
	}

	/**
	 * Check if auth is initialized
	 */
	public isInitialized(): boolean {
		return this.initialized;
	}

	/**
	 * Reset the auth instance (useful for testing)
	 */
	public reset(): void {
		this.authInstance = null;
		this.initialized = false;
	}
}

// Export a convenient function to get the auth instance
export const getAuthManager = (): AuthManager => {
	return AuthManager.getInstance();
};

// Export a function to initialize auth
export const initializeAuth = (db: Db): ReturnType<typeof betterAuth> => {
	return getAuthManager().initialize(db);
};

// Export a function to get the auth instance
export const getAuth = (): ReturnType<typeof betterAuth> => {
	return getAuthManager().getAuth();
};
